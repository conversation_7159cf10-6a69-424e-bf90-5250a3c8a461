# reCAPTCHA Manual Testing Guide

## Overview
This guide provides step-by-step instructions for manually testing the reCAPTCHA implementation in your Laravel application.

## Prerequisites
- Ensure reCAPTCHA is properly configured in `.env`:
  ```
  RECAPTCHA_ENABLED=true
  RECAPTCHA_SITE_KEY=your-site-key
  RECAPTCHA_SECRET_KEY=your-secret-key
  ```

## Manual Testing Steps

### 1. Test reCAPTCHA Display
**Test Case: Verify reCAPTCHA appears on registration forms**

1. **Prime Trial Registration:**
   - Navigate to `/register-prime-trial`
   - Open browser DevTools (F12)
   - Check Network tab for `google.com/recaptcha/api.js` request
   - Check Elements tab for reCAPTCHA script inclusion

2. **Regular Registration:**
   - Navigate to `/register`
   - Repeat same checks as above

### 2. Test reCAPTCHA Functionality
**Test Case: Verify successful registration with valid reCAPTCHA**

1. **Valid Registration:**
   - Fill out the registration form completely
   - Submit the form
   - Verify successful registration (no reCAPTCHA errors)

2. **Invalid reCAPTCHA Simulation:**
   - Open browser DevTools
   - In Console, run: `document.getElementById('g-recaptcha-response').value = 'invalid-token'`
   - Submit the form
   - Verify reCAPTCHA validation error appears

### 3. Test reCAPTCHA Disabled
**Test Case: Verify forms work when reCAPTCHA is disabled**

1. **Disable reCAPTCHA:**
   - Set `RECAPTCHA_ENABLED=false` in `.env`
   - Run: `php artisan config:clear`

2. **Test Forms:**
   - Navigate to registration forms
   - Verify no reCAPTCHA script is loaded
   - Verify registration works without reCAPTCHA

### 4. Test Edge Cases
**Test Case: Verify error handling**

1. **Network Issues:**
   - Disconnect internet after page loads
   - Try to submit form
   - Verify appropriate error message appears

2. **JavaScript Disabled:**
   - Disable JavaScript in browser
   - Try to submit form
   - Verify form handles gracefully

## Automated Testing
Run the following commands to execute automated tests:

```bash
# Run all reCAPTCHA tests
php artisan test tests/Feature/RecaptchaTest.php tests/Feature/RecaptchaDisplayTest.php

# Run specific test
php artisan test tests/Feature/RecaptchaTest.php --filter it_successfully_registers_a_prime_trial_user_with_a_valid_recaptcha_token

# Run with verbose output
php artisan test tests/Feature/RecaptchaTest.php -v
```

## Troubleshooting

### Common Issues
1. **"reCAPTCHA verification failed"**
   - Check secret key configuration
   - Verify domain whitelist in Google reCAPTCHA admin

2. **"Invalid site key"**
   - Verify site key matches Google reCAPTCHA admin
   - Check domain configuration

3. **reCAPTCHA not displaying**
   - Check browser console for JavaScript errors
   - Verify `RECAPTCHA_ENABLED=true` in `.env`

### Debug Commands
```bash
# Check reCAPTCHA configuration
php artisan tinker --execute="print_r(config('services.recaptcha'))"

# Clear configuration cache
php artisan config:clear
php artisan cache:clear
```

## Production Checklist
- [ ] Replace test keys with production keys
- [ ] Add production domain to Google reCAPTCHA admin
- [ ] Test on production domain
- [ ] Monitor error logs for reCAPTCHA failures
- [ ] Set up rate limiting for registration endpoints
