# reCAPTCHA Testing Guide

This guide provides instructions for testing the reCAPTCHA implementation in your Laravel application.

## Automated Testing

### Running Feature Tests
```bash
# Run the Recaptcha feature tests
php artisan test tests/Feature/RecaptchaTest.php

# Run all tests
php artisan test
```

### Test Coverage
- **RecaptchaTest.php**: Contains automated tests for reCAPTCHA validation
  - Tests successful registration with valid reCAPTCHA
  - Tests failed registration with invalid reCAPTCHA

## Manual Testing

### 1. Test reCAPTCHA Validation (Enabled)
1. **Enable reCAPTCHA in production mode**:
   ```bash
   # In .env file
   RECAPTCHA_ENABLED=true
   RECAPTCHA_TEST_VALIDATION=false
   ```

2. **Test with valid reCAPTCHA**:
   - Navigate to any registration form (e.g., `/register/prime-trial`)
   - Fill in all required fields
   - Complete the reCAPTCHA challenge
   - Submit the form
   - **Expected**: Registration should succeed

3. **Test with invalid/missing reCAPTCHA**:
   - Navigate to any registration form
   - Fill in all required fields
   - Don't complete the reCAPTCHA challenge
   - Submit the form
   - **Expected**: Should see error "The reCAPTCHA verification failed. Please try again."

### 2. Test reCAPTCHA Disabled
1. **Disable reCAPTCHA**:
   ```bash
   # In .env file
   RECAPTCHA_ENABLED=false
   ```

2. **Test registration**:
   - Navigate to any registration form
   - Fill in all required fields
   - Submit the form without reCAPTCHA
   - **Expected**: Registration should succeed without reCAPTCHA

### 3. Test reCAPTCHA in Development Mode
1. **Enable test validation**:
   ```bash
   # In .env file
   RECAPTCHA_ENABLED=true
   RECAPTCHA_TEST_VALIDATION=true
   ```

2. **Test with any token**:
   - Use token "valid-token" for successful validation
   - Use any other token for failed validation

## Browser Developer Tools Testing

### Inspect reCAPTCHA Widget
1. Open browser DevTools (F12)
2. Navigate to Elements tab
3. Look for:
   - `<div class="g-recaptcha">` elements
   - reCAPTCHA script loading from google.com
   - Site key in data attributes

### Network Tab Testing
1. Open Network tab in DevTools
2. Submit a form with reCAPTCHA
3. Look for:
   - POST request to `https://www.google.com/recaptcha/api/siteverify`
   - Response containing success/failure status

### Console Testing
```javascript
// Check if reCAPTCHA is loaded
console.log('reCAPTCHA loaded:', typeof grecaptcha !== 'undefined');

// Get reCAPTCHA response
var response = grecaptcha.getResponse();
console.log('reCAPTCHA response:', response);
```

## Common Issues and Solutions

### 1. reCAPTCHA Not Appearing
- **Check**: Site key is correctly configured in `.env`
- **Check**: reCAPTCHA script is loaded in the page
- **Check**: No JavaScript errors in console

### 2. "Invalid site key" Error
- **Solution**: Verify RECAPTCHA_SITE_KEY in `.env` matches Google reCAPTCHA admin console

### 3. "Invalid secret key" Error
- **Solution**: Verify RECAPTCHA_SECRET_KEY in `.env` matches Google reCAPTCHA admin console

### 4. Local Development Issues
- **Solution**: Add `localhost` to allowed domains in reCAPTCHA admin console
- **Solution**: Use test keys provided by Google for development

## Environment Configuration

### Required .env Variables
```bash
# reCAPTCHA Configuration
RECAPTCHA_ENABLED=true
RECAPTCHA_SITE_KEY=your-site-key-here
RECAPTCHA_SECRET_KEY=your-secret-key-here
RECAPTCHA_TEST_VALIDATION=false  # Set to true for testing
```

### Testing Configuration
```bash
# For automated tests
RECAPTCHA_ENABLED=true
RECAPTCHA_TEST_VALIDATION=true
```

## Verification Checklist

- [ ] reCAPTCHA widget appears on registration forms
- [ ] Forms validate reCAPTCHA on submission
- [ ] Invalid reCAPTCHA shows appropriate error messages
- [ ] Valid reCAPTCHA allows successful form submission
- [ ] Automated tests pass
- [ ] Configuration works in both development and production
- [ ] Site key and secret key are correctly configured
- [ ] reCAPTCHA admin console shows successful verifications
