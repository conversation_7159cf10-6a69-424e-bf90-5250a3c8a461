# reCAPTCHA Domain Configuration Guide

## Issue: "Invalid domain for site key" Error

This error occurs when your reCAPTCHA site key is configured for a different domain than the one you're currently using.

## Solutions

### Option 1: Create Development reCAPTCHA Keys (Recommended)

1. **Go to Google reCAPTCHA Admin Console**
   - Visit: https://www.google.com/recaptcha/admin/
   - Sign in with your Google account

2. **Create a new site**
   - Click the "+" button to add a new site
   - **Label**: `BizappOS Development`
   - **reCAPTCHA type**: Select `reCAPTCHA v3`
   - **Domains**: Add these domains:
     ```
     localhost
     127.0.0.1
     bizappos_laravel.test
     *.test
     ```
   - Accept the terms and submit

3. **Update your .env file**
   ```env
   RECAPTCHA_ENABLED=true
   RECAPTCHA_V3_SITE_KEY=your_new_development_site_key
   RECAPTCHA_V3_SECRET_KEY=your_new_development_secret_key
   ```

### Option 2: Update Existing reCAPTCHA Configuration

If you have access to the existing reCAPTCHA configuration:

1. **Go to Google reCAPTCHA Admin Console**
   - Visit: https://www.google.com/recaptcha/admin/
   - Find your existing site (with current site key)

2. **Edit the site settings**
   - Click on the site name
   - Go to "Settings" tab
   - Add development domains to the domain list:
     ```
     localhost
     127.0.0.1
     bizappos_laravel.test
     *.test
     ```

### Option 3: Environment-Specific Configuration

Create different configurations for different environments:

#### Development (.env.local)
```env
RECAPTCHA_ENABLED=false
# OR use development keys:
# RECAPTCHA_ENABLED=true
# RECAPTCHA_V3_SITE_KEY=your_development_site_key
# RECAPTCHA_V3_SECRET_KEY=your_development_secret_key
```

#### Production (.env.production)
```env
RECAPTCHA_ENABLED=true
RECAPTCHA_V3_SITE_KEY=your_production_site_key
RECAPTCHA_V3_SECRET_KEY=your_production_secret_key
```

## Current Status

✅ **Temporarily disabled reCAPTCHA for development**
- Set `RECAPTCHA_ENABLED=false` in your `.env` file
- This allows you to test the forms without reCAPTCHA errors
- Remember to re-enable for production!

## Testing

After updating your configuration, test with:

```bash
# Test that reCAPTCHA is properly disabled/enabled
php artisan test tests/Feature/RecaptchaDisplayTest.php

# Test the registration forms
php artisan test tests/Feature/RecaptchaTest.php
```

## Production Deployment

Before deploying to production:

1. ✅ Ensure you have valid production reCAPTCHA keys
2. ✅ Set `RECAPTCHA_ENABLED=true` in production
3. ✅ Verify the production domain is added to reCAPTCHA configuration
4. ✅ Test the forms on the production domain

## Troubleshooting

- **"Invalid domain for site key"**: Domain not added to reCAPTCHA configuration
- **"Invalid site key"**: Wrong site key or key doesn't exist
- **"Invalid secret key"**: Wrong secret key in backend validation
- **reCAPTCHA not loading**: Check if `RECAPTCHA_ENABLED=true` and keys are set

## Security Notes

- Never commit real reCAPTCHA keys to version control
- Use different keys for development and production
- Regularly rotate your secret keys
- Monitor reCAPTCHA analytics for suspicious activity
