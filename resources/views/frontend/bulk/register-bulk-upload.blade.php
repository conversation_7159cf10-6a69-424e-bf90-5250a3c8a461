<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload System</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.2/axios.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
    
    @if(config('services.recaptcha.enabled', false))
        <script src="https://www.google.com/recaptcha/api.js?render={{ config('services.recaptcha.site_key') }}"></script>
    @endif
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
            background-color: #f5f5f5;
        }

        .upload-container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .info-text {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .file-input {
            display: block;
            width: 100%;
            padding: 0.5rem;
            border: 2px dashed #ccc;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .mapping-container {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .mapping-group {
            margin-bottom: 1rem;
        }

        .mapping-pair {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }

        input[type="text"] {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            flex: 1;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .status-container {
            margin-top: 2rem;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: none;
        }

        .progress-bar {
            height: 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-bar-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }

        .error {
            color: #dc3545;
            margin-top: 0.5rem;
        }

        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="upload-container">
        <h1>File Upload System</h1>
        <p class="info-text">Note: All users will be created with the default password: 123456</p>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="file">Choose File (CSV, XLSX, XLS)</label>
                <input type="file" id="file" class="file-input" accept=".csv,.xlsx,.xls" required>
            </div>

            <div class="mapping-container">
                <h3>Column Mapping</h3>
                
                <div class="mapping-group">
                    <h4>User Fields</h4>
                    <div class="mapping-pair">
                        <input type="text" name="users[name]" placeholder="Name Column" value="Name" required>
                        <input type="text" name="users[email]" placeholder="Email Column" value="Email" required>
                    </div>
                </div>

                <div class="mapping-group">
                    <h4>User Details Fields</h4>
                    <div class="mapping-pair">
                        <input type="text" name="user_details[phone]" placeholder="Phone Column" value="Phone" required>
                        <input type="text" name="user_details[address]" placeholder="Address Column" value="Address" required>
                    </div>
                </div>

                <div class="mapping-group">
                    <h4>Company Fields</h4>
                    <div class="mapping-pair">
                        <input type="text" name="companies[name]" placeholder="Company Name Column" value="Company" required>
                        <input type="text" name="companies[address]" placeholder="Company Address Column" value="Company Address" required>
                    </div>
                </div>
            </div>

            <!-- Hidden reCAPTCHA token field -->
            <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response">
            
            <!-- Honeypot field - hidden from users but bots might fill it -->
            <div style="display: none;">
                <label for="honeypot">Leave this field empty</label>
                <input type="text" name="honeypot" id="honeypot" tabindex="-1" autocomplete="off">
            </div>

            <button type="submit" id="submitBtn">Upload and Process</button>
        </form>

        <div id="statusContainer" class="status-container">
            <h3>Processing Status</h3>
            <div class="progress-bar">
                <div id="progressBar" class="progress-bar-fill" style="width: 0%"></div>
            </div>
            <p id="statusText">Uploading file...</p>
            <div id="errorContainer" class="error"></div>
        </div>
    </div>

    <script>
       
        const form = document.getElementById('uploadForm');
        const statusContainer = document.getElementById('statusContainer');
        const progressBar = document.getElementById('progressBar');
        const statusText = document.getElementById('statusText');
        const errorContainer = document.getElementById('errorContainer');
        const submitBtn = document.getElementById('submitBtn');

        let statusCheckInterval;

        function getColumnMapping() {
            const mapping = {
                users: {},
                user_details: {},
                companies: {}
            };

            // Get all input fields and organize them into mapping object
            const inputs = form.querySelectorAll('input[type="text"]');
            inputs.forEach(input => {
                const name = input.name;
                const value = input.value;
                
                if (name.startsWith('users[')) {
                    const key = name.match(/\[(.*?)\]/)[1];
                    mapping.users[key] = value;
                } else if (name.startsWith('user_details[')) {
                    const key = name.match(/\[(.*?)\]/)[1];
                    mapping.user_details[key] = value;
                } else if (name.startsWith('companies[')) {
                    const key = name.match(/\[(.*?)\]/)[1];
                    mapping.companies[key] = value;
                }
            });

            return mapping;
        }

        async function checkStatus(jobId) {
            try {
                const response = await axios.get(`/api/upload/status/${jobId}`);
                const { status, total_rows, processed_rows, errors } = response.data;

                // Update progress bar
                if (total_rows > 0) {
                    const progress = (processed_rows / total_rows) * 100;
                    progressBar.style.width = `${progress}%`;
                    statusText.textContent = `Processing: ${processed_rows} of ${total_rows} rows (${Math.round(progress)}%)`;
                }

                // Handle different statuses
                if (status === 'completed') {
                    clearInterval(statusCheckInterval);
                    statusText.textContent = 'Processing completed successfully!';
                    statusText.className = 'success';
                    submitBtn.disabled = false;
                } else if (status === 'failed') {
                    clearInterval(statusCheckInterval);
                    statusText.textContent = 'Processing failed';
                    errorContainer.textContent = errors ? JSON.stringify(errors, null, 2) : 'Unknown error occurred';
                    submitBtn.disabled = false;
                }
            } catch (error) {
                console.error('Error checking status:', error);
                errorContainer.textContent = 'Error checking status: ' + error.message;
            }
        }

        @if(config('services.recaptcha.enabled', false))
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Execute reCAPTCHA before form submission
            grecaptcha.ready(async function() {
                try {
                    const token = await grecaptcha.execute('{{ config('services.recaptcha.site_key') }}', {action: 'bulk_register'});
                    document.getElementById('g-recaptcha-response').value = token;
                    
                    // Reset status elements
                    statusContainer.style.display = 'block';
                    progressBar.style.width = '0%';
                    statusText.textContent = 'Uploading file...';
                    statusText.className = '';
                    errorContainer.textContent = '';
                    submitBtn.disabled = true;

                    try {
                        const formData = new FormData();
                        formData.append('file', document.getElementById('file').files[0]);
                        formData.append('column_mapping', JSON.stringify(getColumnMapping()));
                        formData.append('g-recaptcha-response', token);

                        // Upload file
                        const response = await axios.post('/api/process-bulk-register', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        });

                        if (response.data.success) {
                            statusText.textContent = 'File uploaded, starting processing...';
                            
                            // Start checking status
                            statusCheckInterval = setInterval(() => {
                                checkStatus(response.data.job_id);
                            }, 2000);
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                        statusText.textContent = 'Upload failed';
                        errorContainer.textContent = error.response?.data?.message || error.message;
                        submitBtn.disabled = false;
                    }
                } catch (error) {
                    console.error('reCAPTCHA error:', error);
                    errorContainer.textContent = 'reCAPTCHA verification failed. Please try again.';
                    submitBtn.disabled = false;
                }
            });
        });
        @else
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Reset status elements
            statusContainer.style.display = 'block';
            progressBar.style.width = '0%';
            statusText.textContent = 'Uploading file...';
            statusText.className = '';
            errorContainer.textContent = '';
            submitBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('file', document.getElementById('file').files[0]);
                formData.append('column_mapping', JSON.stringify(getColumnMapping()));

                // Upload file
                const response = await axios.post('/api/process-bulk-register', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });

                if (response.data.success) {
                    statusText.textContent = 'File uploaded, starting processing...';
                    
                    // Start checking status
                    statusCheckInterval = setInterval(() => {
                        checkStatus(response.data.job_id);
                    }, 2000);
                }
            } catch (error) {
                console.error('Upload error:', error);
                statusText.textContent = 'Upload failed';
                errorContainer.textContent = error.response?.data?.message || error.message;
                submitBtn.disabled = false;
            }
        });
        @endif
    </script>
</body>
</html>
