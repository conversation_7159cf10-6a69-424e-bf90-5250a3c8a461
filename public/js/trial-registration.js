document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 4;

    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    const submitBtn = document.getElementById('submitBtn');
    const form = document.getElementById('trialRegistrationForm');

    // Check if this is a multi-step form or single-step form
    const isMultiStep = nextBtn && prevBtn;

    // Initialize wizard only if it's a multi-step form
    if (isMultiStep) {
        showStep(currentStep);
    }
    
    // Next button click (only if multi-step)
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    showStep(currentStep);
                }
            }
        });
    }

    // Previous button click (only if multi-step)
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        });
    }
    
    // Form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            // For single-step forms, don't prevent submission
            if (!isMultiStep) {
                return true;
            }

            // For multi-step forms, validate current step
            if (currentStep !== totalSteps || !validateCurrentStep()) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>Creating your account...';
                submitBtn.disabled = true;
            }
        });
    }
    
    function showStep(step) {
        // Hide all steps
        document.querySelectorAll('.step-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // Show current step
        const currentStepContent = document.querySelector(`.step-content[data-step="${step}"]`);
        if (currentStepContent) {
            currentStepContent.classList.add('active');
        }
        
        // Update step indicators
        updateStepIndicators(step);
        
        // Update navigation buttons
        updateNavigationButtons(step);
        
        // Special handling for step 4 (review)
        if (step === 4 && window.populateReviewInformation) {
            window.populateReviewInformation();
        }
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    
    function updateStepIndicators(step) {
        document.querySelectorAll('.step-indicator .step').forEach((indicator, index) => {
            const stepNumber = index + 1;
            indicator.classList.remove('active', 'completed');
            
            if (stepNumber < step) {
                indicator.classList.add('completed');
            } else if (stepNumber === step) {
                indicator.classList.add('active');
            }
        });
    }
    
    function updateNavigationButtons(step) {
        // Only update navigation if buttons exist (multi-step form)
        if (!isMultiStep) return;

        // Previous button
        if (prevBtn) {
            if (step === 1) {
                prevBtn.style.display = 'none';
            } else {
                prevBtn.style.display = 'inline-block';
            }
        }

        // Next/Submit buttons
        if (nextBtn && submitBtn) {
            if (step === totalSteps) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'inline-block';
                submitBtn.style.display = 'none';
            }
        }
    }
    
    function validateCurrentStep() {
        const currentStepContent = document.querySelector(`.step-content[data-step="${currentStep}"]`);
        const inputs = currentStepContent.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        // Clear previous error messages
        currentStepContent.querySelectorAll('.error-message').forEach(error => {
            error.remove();
        });
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                showFieldError(input, 'This field is required');
                isValid = false;
            } else {
                // Additional validation based on input type
                if (input.type === 'email' && !isValidEmail(input.value)) {
                    showFieldError(input, 'Please enter a valid email address');
                    isValid = false;
                }
                
                if (input.name === 'password' && input.value.length < 8) {
                    showFieldError(input, 'Password must be at least 8 characters long');
                    isValid = false;
                }
                
                if (input.name === 'password_confirmation') {
                    const password = document.getElementById('password').value;
                    if (input.value !== password) {
                        showFieldError(input, 'Passwords do not match');
                        isValid = false;
                    }
                }
                
                if (input.name === 'mobile' && input.value.length < 10) {
                    showFieldError(input, 'Please enter a valid mobile number');
                    isValid = false;
                }
                
                if (input.name === 'postcode' && input.value.length !== 5) {
                    showFieldError(input, 'Postcode must be 5 digits');
                    isValid = false;
                }
                
                if (input.name === 'business_postcode' && input.value.length !== 5) {
                    showFieldError(input, 'Business postcode must be 5 digits');
                    isValid = false;
                }
            }
        });
        
        // Special validation for step 4 (terms agreement)
        if (currentStep === 4) {
            const agreeTerms = document.getElementById('agreeTerms');
            if (!agreeTerms.checked) {
                showFieldError(agreeTerms, 'You must agree to the terms and conditions');
                isValid = false;
            }
        }
        
        return isValid;
    }
    
    function showFieldError(input, message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message text-danger mt-1';
        errorElement.textContent = message;
        
        // Insert error message after the input's parent group
        const formGroup = input.closest('.form-group');
        if (formGroup) {
            formGroup.appendChild(errorElement);
        }
        
        // Add error styling to input
        input.classList.add('is-invalid');
        
        // Remove error styling when user starts typing
        input.addEventListener('input', function() {
            input.classList.remove('is-invalid');
            if (errorElement.parentNode) {
                errorElement.remove();
            }
        }, { once: true });
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Initialize default values and fix dropdown display for trial form
    function initializeTrialFormDefaults() {
        const countrySelect = document.getElementById('country');
        const businessCountrySelect = document.getElementById('business_country');

        // Set Malaysia as default if no value is selected
        if (countrySelect && !countrySelect.value) {
            countrySelect.value = 'Malaysia';
            // Trigger change event to ensure display updates
            countrySelect.dispatchEvent(new Event('change'));
        }

        if (businessCountrySelect && !businessCountrySelect.value) {
            businessCountrySelect.value = 'Malaysia';
            // Trigger change event to ensure display updates
            businessCountrySelect.dispatchEvent(new Event('change'));
        }

        // Force refresh of all trial form select elements to show their values
        const trialSelects = document.querySelectorAll('.trial-form-select');
        trialSelects.forEach(select => {
            if (select.value) {
                select.style.color = '#495057';
            } else {
                select.style.color = '#6c757d';
            }
        });
    }

    // Initialize default values
    initializeTrialFormDefaults();

    // Add event listeners to update trial form select styling when values change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('trial-form-select')) {
            if (e.target.value) {
                e.target.style.color = '#495057';
            } else {
                e.target.style.color = '#6c757d';
            }
        }
    });
    
    // Auto-save form data to localStorage (optional)
    function saveFormData() {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        localStorage.setItem('trialRegistrationData', JSON.stringify(data));
    }
    
    function loadFormData() {
        const savedData = localStorage.getItem('trialRegistrationData');
        if (savedData) {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input && input.type !== 'password') {
                    input.value = data[key];
                }
            });
        }
    }
    
    // Save form data on input change (only if form exists)
    if (form) {
        form.addEventListener('input', saveFormData);

        // Load saved data on page load
        loadFormData();

        // Clear saved data on successful submission
        form.addEventListener('submit', function() {
            localStorage.removeItem('trialRegistrationData');
        });
    }
    
    // Keyboard navigation (only for multi-step forms)
    if (isMultiStep) {
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                if (currentStep < totalSteps && nextBtn) {
                    nextBtn.click();
                } else if (submitBtn) {
                    submitBtn.click();
                }
            }
        });
    }
});
