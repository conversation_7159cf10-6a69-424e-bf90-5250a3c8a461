<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Subscription\SubscriptionPlan;
use App\Models\Feature;
use Illuminate\Support\Facades\Log;

class TrialPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if the trial plan already exists
        $trialPlan = SubscriptionPlan::where('name', 'Starter - Trial')->first();
        
        if ($trialPlan) {
            Log::info('Trial plan already exists, skipping creation');
            return;
        }
        
        // Create the trial plan
        $trialPlan = SubscriptionPlan::create([
            'name' => 'Starter - Trial',
            'description' => '12-day trial plan with basic features',
            'price' => 0,
            'duration_in_seconds' => 604800, // 7 days in seconds
            'tier' => 'starter',
            'billing_frequency' => 'monthly',
            'trial_period_days' => 0, // This is not a trial of another plan
            'is_active' => true
        ]);
        
        Log::info('Created trial plan', ['plan_id' => $trialPlan->id]);
        
        // Find core features to attach to the trial plan
        $coreFeatures = Feature::where('type', 'core')
            ->where('is_active', true)
            ->get();
            
        // Find limit features
        $productSkuLimitFeature = Feature::where('code', 'CF-PRODUCT-SKU-LIMIT')->first();
        $staffAccountLimitFeature = Feature::where('code', 'CF-STAFF-ACCOUNT-LIMIT')->first();
        
        // Attach core features
        foreach ($coreFeatures as $feature) {
            $trialPlan->features()->attach($feature->id);
        }
        
        // Attach limit features with restricted limits
        if ($productSkuLimitFeature) {
            $trialPlan->features()->attach($productSkuLimitFeature->id, [
                'settings' => json_encode(['limit' => 50]) // Limit to 50 SKUs for trial
            ]);
        }
        
        if ($staffAccountLimitFeature) {
            $trialPlan->features()->attach($staffAccountLimitFeature->id, [
                'settings' => json_encode(['limit' => 1]) // Limit to 1 staff account for trial
            ]);
        }
        
        Log::info('Attached features to trial plan', [
            'plan_id' => $trialPlan->id,
            'core_features_count' => $coreFeatures->count(),
            'has_sku_limit' => $productSkuLimitFeature ? 'yes' : 'no',
            'has_staff_limit' => $staffAccountLimitFeature ? 'yes' : 'no'
        ]);
    }
}
