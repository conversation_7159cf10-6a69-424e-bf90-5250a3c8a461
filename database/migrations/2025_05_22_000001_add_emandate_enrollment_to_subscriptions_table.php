<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Check if the column doesn't already exist before adding it
            if (!Schema::hasColumn('subscriptions', 'emandate_enrollment_id')) {
                $table->foreignUuid('emandate_enrollment_id')
                      ->nullable()
                      ->after('bill_type')
                      ->constrained('bayarcash_emandate_enrollments')
                      ->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Check if the column exists before trying to drop it
            if (Schema::hasColumn('subscriptions', 'emandate_enrollment_id')) {
                $table->dropForeign(['emandate_enrollment_id']);
                $table->dropColumn('emandate_enrollment_id');
            }
        });
    }
};
