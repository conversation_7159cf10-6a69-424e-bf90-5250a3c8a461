<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->enum('tier', ['basic', 'premium', 'enterprise'])->default('basic')->after('price');
            $table->enum('billing_frequency', ['monthly', 'yearly'])->default('monthly')->after('tier');
            $table->integer('trial_period_days')->default(0)->after('duration_in_seconds');
            $table->boolean('is_active')->default(true)->after('features');
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscription_plans', function (Blueprint $table) {
            $table->dropColumn(['tier', 'billing_frequency', 'trial_period_days', 'is_active']);
            $table->dropSoftDeletes();
        });
    }
};
