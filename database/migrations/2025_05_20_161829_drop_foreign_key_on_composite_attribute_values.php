<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //drop foreign key on composite_attribute_values table
        Schema::table('composite_attribute_values', function (Blueprint $table) {
            if (DB::getDriverName() !== 'sqlite') {
                $table->dropForeign(['variant_attribute_value_id']);
            }
            // $table->dropForeign(['composite_attribute_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // add foreign key on composite_attribute_values table
        // Schema::table('composite_attribute_values', function (Blueprint $table) {
        //     $table->foreign('variant_attribute_value_id')->references('id')->on('variant_attribute_values')->onDelete('cascade')->onUpdate('cascade');
        //     // $table->foreign('composite_attribute_id')->references('id')->on('composite_attributes')->onDelete('cascade')->onUpdate('cascade');
        // });
    }
};
