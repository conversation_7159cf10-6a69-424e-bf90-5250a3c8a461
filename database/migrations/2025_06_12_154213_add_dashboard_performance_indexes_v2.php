<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations for dashboard performance optimization
     */
    public function up()
    {
        // Orders table indexes - check if they don't exist before creating
        Schema::table('orders', function (Blueprint $table) {
            // For dataSales function
            if (!$this->indexExists('orders', 'idx_orders_user_date_deleted')) {
                $table->index(['user_id', 'order_date', 'deleted_at'], 'idx_orders_user_date_deleted');
            }

            // For statisticActive function
            if (!$this->indexExists('orders', 'idx_orders_company_date')) {
                $table->index(['company_id', 'order_date'], 'idx_orders_company_date');
            }

            // For heatmap queries
            if (!$this->indexExists('orders', 'idx_orders_location_date')) {
                $table->index(['order_latitude', 'order_longitude', 'order_date'], 'idx_orders_location_date');
            }

            // For payment method analysis
            if (!$this->indexExists('orders', 'idx_orders_payment_date')) {
                $table->index(['pay_method', 'order_date', 'deleted_at'], 'idx_orders_payment_date');
            }
        });

        // Companies table indexes
        Schema::table('companies', function (Blueprint $table) {
            // For user-company relationships
            if (!$this->indexExists('companies', 'idx_companies_user_created')) {
                $table->index(['user_id', 'created_at'], 'idx_companies_user_created');
            }

            // For PBT relationships
            if (!$this->indexExists('companies', 'idx_companies_pbt_category')) {
                $table->index(['pbt_id', 'category_id'], 'idx_companies_pbt_category');
            }
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            // For access module filtering
            if (!$this->indexExists('users', 'idx_users_access_username')) {
                $table->index(['access_module', 'username'], 'idx_users_access_username');
            }
        });

        // PBTs table indexes
        Schema::table('pbts', function (Blueprint $table) {
            // For state and status filtering
            if (!$this->indexExists('pbts', 'idx_pbts_state_status')) {
                $table->index(['state', 'status'], 'idx_pbts_state_status');
            }
        });

        // Products table indexes - fix column name from 'name' to 'product_name'
        Schema::table('products', function (Blueprint $table) {
            // For stock analysis
            if (!$this->indexExists('products', 'idx_products_stock_name')) {
                $table->index(['product_stock', 'product_name'], 'idx_products_stock_name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            if ($this->indexExists('orders', 'idx_orders_user_date_deleted')) {
                $table->dropIndex('idx_orders_user_date_deleted');
            }
            if ($this->indexExists('orders', 'idx_orders_company_date')) {
                $table->dropIndex('idx_orders_company_date');
            }
            if ($this->indexExists('orders', 'idx_orders_location_date')) {
                $table->dropIndex('idx_orders_location_date');
            }
            if ($this->indexExists('orders', 'idx_orders_payment_date')) {
                $table->dropIndex('idx_orders_payment_date');
            }
        });

        Schema::table('companies', function (Blueprint $table) {
            if ($this->indexExists('companies', 'idx_companies_user_created')) {
                $table->dropIndex('idx_companies_user_created');
            }
            if ($this->indexExists('companies', 'idx_companies_pbt_category')) {
                $table->dropIndex('idx_companies_pbt_category');
            }
        });

        Schema::table('users', function (Blueprint $table) {
            if ($this->indexExists('users', 'idx_users_access_username')) {
                $table->dropIndex('idx_users_access_username');
            }
        });

        Schema::table('pbts', function (Blueprint $table) {
            if ($this->indexExists('pbts', 'idx_pbts_state_status')) {
                $table->dropIndex('idx_pbts_state_status');
            }
        });

        Schema::table('products', function (Blueprint $table) {
            if ($this->indexExists('products', 'idx_products_stock_name')) {
                $table->dropIndex('idx_products_stock_name');
            }
        });
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists($table, $indexName)
    {
        // For SQLite compatibility, we'll just return false to always create the index
        // This is safer than trying to check for existing indexes in a cross-database way
        if (\DB::getDriverName() === 'sqlite') {
            return false;
        }
        
        // For MySQL, use the original logic
        $indexes = \DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
        return !empty($indexes);
    }
};
