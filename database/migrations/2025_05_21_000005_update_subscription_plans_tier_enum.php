<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, modify any existing data to match new enum values
        DB::table('subscription_plans')
            ->where('tier', '=', 'basic')
            ->update(['tier' => 'starter']);
        
        DB::table('subscription_plans')
            ->where('tier', '=', 'premium')
            ->update(['tier' => 'plus']);
            
        DB::table('subscription_plans')
            ->where('tier', '=', 'enterprise')
            ->update(['tier' => 'advance']);

        // Then alter the enum (only for MySQL, SQLite doesn't support this)
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE `subscription_plans` MODIFY COLUMN `tier` ENUM('starter', 'plus', 'advance') DEFAULT 'starter'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, modify any existing data back to original values
        DB::table('subscription_plans')
            ->where('tier', '=', 'starter')
            ->update(['tier' => 'basic']);
            
        DB::table('subscription_plans')
            ->where('tier', '=', 'plus')
            ->update(['tier' => 'premium']);
            
        DB::table('subscription_plans')
            ->where('tier', '=', 'advance')
            ->update(['tier' => 'enterprise']);

        // Then revert the enum (only for MySQL, SQLite doesn't support this)
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE `subscription_plans` MODIFY COLUMN `tier` ENUM('basic', 'premium', 'enterprise') DEFAULT 'basic'");
        }
    }
};
