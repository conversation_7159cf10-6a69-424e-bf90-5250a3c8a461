<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // MySQL doesn't allow direct modification of ENUM values
        // We need to modify the column type to a string first, then back to ENUM with new values

        // Step 1: Change the column type to string
        Schema::table('features', function (Blueprint $table) {
            $table->string('type', 20)->change();
        });

        // Step 2: Change the column back to ENUM with the new values (only for MySQL)
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE features MODIFY COLUMN type ENUM('core', 'addon', 'limit_upgrade') DEFAULT 'addon'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Step 1: Change the column type to string
        Schema::table('features', function (Blueprint $table) {
            $table->string('type', 20)->change();
        });

        // Step 2: Change the column back to ENUM with the original values (only for MySQL)
        if (DB::getDriverName() !== 'sqlite') {
            DB::statement("ALTER TABLE features MODIFY COLUMN type ENUM('core', 'addon') DEFAULT 'addon'");
        }
    }
};
