<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Company;
use App\Models\Feature;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;

return new class extends Migration
{
    /**
     * Run the migrations.
     * This migration creates a special "Legacy Unlimited" plan for existing users
     * and assigns all existing companies to this plan.
     */
    public function up(): void
    {
        // Check if required tables exist before proceeding
        if (!Schema::hasTable('subscription_plans') || !Schema::hasTable('features') || !Schema::hasTable('companies')) {
            return;
        }

        // Check if legacy plan already exists to avoid duplicates
        $existingLegacyPlan = SubscriptionPlan::where('name', 'Legacy Unlimited')->first();
        if ($existingLegacyPlan) {
            return;
        }

        // Create a special "Legacy Unlimited" plan for existing users
        $legacyPlan = SubscriptionPlan::create([
            'name' => 'Legacy Unlimited',
            'description' => 'Special plan for existing users with unlimited features',
            'price' => 0,
            'duration_in_seconds' => ********, // 1 year in seconds
            'tier' => 'enterprise', // Set to highest tier (using valid value for this migration)
            'billing_frequency' => 'yearly',
            'trial_period_days' => 0,
            'is_active' => true
        ]);

        // Get the limit features
        $productSkuLimitFeature = Feature::where('code', 'product_sku_limit')->first();
        $staffAccountLimitFeature = Feature::where('code', 'staff_account_limit')->first();

        // Attach features with unlimited values to the legacy plan
        if ($productSkuLimitFeature) {
            $legacyPlan->features()->attach($productSkuLimitFeature->id, [
                'settings' => json_encode(['limit' => -1]) // -1 means unlimited
            ]);
        }
        
        if ($staffAccountLimitFeature) {
            $legacyPlan->features()->attach($staffAccountLimitFeature->id, [
                'settings' => json_encode(['limit' => -1]) // -1 means unlimited
            ]);
        }

        // Get all companies that don't have an active subscription
        $companies = Company::whereDoesntHave('activeSubscription')->get();

        // Create subscriptions for these companies with the legacy plan
        foreach ($companies as $company) {
            Subscription::create([
                'company_id' => $company->id,
                'subscription_plan_id' => $legacyPlan->id,
                'starts_at' => now(),
                'ends_at' => now()->addYear(), // 1 year from now
                'status' => 'active'
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if required tables exist before proceeding
        if (!Schema::hasTable('subscription_plans') || !Schema::hasTable('subscriptions')) {
            return;
        }

        // Find the legacy plan
        $legacyPlan = SubscriptionPlan::where('name', 'Legacy Unlimited')->first();
        
        if ($legacyPlan) {
            // Delete all subscriptions associated with this plan
            Subscription::where('subscription_plan_id', $legacyPlan->id)->delete();
            
            // Delete the plan
            $legacyPlan->delete();
        }
    }
};
