<?php

namespace Tests\Feature;

use App\Models\User;
use App\Rules\Recaptcha;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Mockery;
use Tests\TestCase;

class RecaptchaTest extends TestCase
{
    use RefreshDatabase;

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     */
    public function it_successfully_registers_a_prime_trial_user_with_a_valid_recaptcha_token()
    {
        // Disable Recaptcha validation for this test
        config(['services.recaptcha.enabled' => false]);

        // Mock the Mail facade to prevent actual email sending
        Mail::fake();

        $userData = [
            'email' => '<EMAIL>',
            'business_name' => 'Test Business',
            'phone' => '0123456789',
            'g-recaptcha-response' => 'a-valid-recaptcha-token',
        ];

        $response = $this->post(route('register.prime.trial.process'), $userData);

        $response->assertRedirect();
        $response->assertSessionHas('success');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'username' => 'test-business',
        ]);

        // Verify that the welcome email was sent
        Mail::assertSent(\App\Mail\WelcomeTrialUser::class);
    }

    /**
     * @test
     */
    public function it_fails_to_register_a_prime_trial_user_with_an_invalid_recaptcha_token()
    {
        // Enable Recaptcha validation in testing environment
        config(['services.recaptcha.test_validation' => true]);
        
        // Mock the Recaptcha rule to simulate a failed validation
        $this->mock(Recaptcha::class, function ($mock) {
            $mock->shouldReceive('validate')->andReturnUsing(function ($attribute, $value, $fail) {
                $fail('The reCAPTCHA verification failed. Please try again.');
            });
        });

        $userData = [
            'email' => '<EMAIL>',
            'business_name' => 'Test Business',
            'phone' => '0123456789',
            'g-recaptcha-response' => 'an-invalid-recaptcha-token',
        ];

        $response = $this->post(route('register.prime.trial.process'), $userData);

        $response->assertSessionHasErrors('g-recaptcha-response');
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }
}
