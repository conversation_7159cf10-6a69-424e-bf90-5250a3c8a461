<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RecaptchaDisplayTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @test
     */
    public function it_displays_recaptcha_on_prime_trial_registration_form()
    {
        config(['services.recaptcha.enabled' => true]);
        config(['services.recaptcha.site_key' => 'test-site-key-123']);

        $response = $this->get(route('register.prime.trial'));

        $response->assertStatus(200);
        $response->assertSee('g-recaptcha');
        $response->assertSee('test-site-key-123');
    }

    /**
     * @test
     */
    public function it_does_not_display_recaptcha_when_disabled()
    {
        config(['services.recaptcha.enabled' => false]);

        $response = $this->get(route('register.prime.trial'));

        $response->assertStatus(200);
        $response->assertDontSee('google.com/recaptcha/api.js');
    }

    /**
     * @test
     */
    public function it_displays_recaptcha_on_regular_registration_form()
    {
        config(['services.recaptcha.enabled' => true]);
        config(['services.recaptcha.site_key' => 'test-site-key-123']);

        $response = $this->get(route('register'));

        $response->assertStatus(200);
        $response->assertSee('g-recaptcha');
        $response->assertSee('test-site-key-123');
    }
}
