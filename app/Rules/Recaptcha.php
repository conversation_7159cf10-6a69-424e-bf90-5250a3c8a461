<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Http;

class Recaptcha implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Skip validation if in testing environment and not explicitly enabled
        if (app()->environment('testing') && !config('services.recaptcha.test_validation', false)) {
            return;
        }

        // Check if reCAPTCHA is enabled
        if (!config('services.recaptcha.enabled', false)) {
            return;
        }

        // Verify the reCAPTCHA response
        $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => config('services.recaptcha.secret_key'),
            'response' => $value,
            'remoteip' => request()->ip(),
        ]);

        // Check if the verification was successful
        if (!$response->successful() || !$response->json('success')) {
            $fail('The reCAPTCHA verification failed. Please try again.');
        }

        // Check the score (reCAPTCHA v3 returns a score between 0.0 and 1.0)
        $score = $response->json('score', 0);
        $threshold = config('services.recaptcha.threshold', 0.5);

        if ($score < $threshold) {
            $fail('The reCAPTCHA verification failed. Please try again.');
        }
    }
}
