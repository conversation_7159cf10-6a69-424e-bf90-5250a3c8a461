<?php

namespace App\Http\Controllers\Payment;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use App\Models\Payment\Bizappay;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Http\Controllers\API\BizappayController;

class SubscriptionPaymentController extends Controller
{
    /**
     * Redirect to payment gateway for subscription payment
     */
    public function processPayment(Request $request, $subscriptionId)
    {
        // Get the subscription
        $subscription = Subscription::with('subscriptionPlan', 'company.user')->findOrFail($subscriptionId);
        $user = auth()->user();

        // Security check - ensure the subscription belongs to the authenticated user
        if ($subscription->company->user_id !== $user->id) {
            return redirect()->route('login')->withError('Unauthorized access to subscription payment.');
        }

        try {
            // Get Bizappay token (using sandbox for testing)
            $bizappayController = new BizappayController();

            try {
                $bizappayToken = $bizappayController->generateToken(true); // true = use sandbox
            } catch (\Exception $e) {
                // Handle specific error codes from BizappayController
                $errorCode = $e->getCode();
                $errorMessage = $e->getMessage();

                Log::error('Failed to generate Bizappay token', [
                    'error_code' => $errorCode,
                    'error_message' => $errorMessage,
                    'subscription_id' => $subscription->id
                ]);

                // Provide user-friendly error messages based on error code
                if ($errorCode == 1001) {
                    // API key owner not found error
                    return redirect()->route('dashboard')
                        ->withError('We are currently experiencing technical issues with our payment provider. Our team has been notified and is working to resolve this. Please try again later or contact customer support.');
                } else {
                    // Other errors
                    return redirect()->route('dashboard')
                        ->withError('Payment service is temporarily unavailable. Please try again later or contact customer support.');
                }
            }

            // Get configuration (using sandbox for testing)
            $getApiKey = config('services.bizappay.keySandbox');
            $bizappayUrl = config('services.bizappay.urlSandbox');
            $categoryCode = '4z6h3kuf'; // Sandbox category code

            // Format the amount with 2 decimal places
            $amount = number_format($subscription->subscriptionPlan->price, 2, '.', '');

            try {
                // Create bill in Bizappay
                $response = Http::withHeaders([
                    'Authentication' => $bizappayToken,
                ])
                ->asForm()
                ->post($bizappayUrl . 'bill/create', [
                    'apiKey' => $getApiKey,
                    'category' => $categoryCode,
                    'name' => 'Bizappos ' . $subscription->subscriptionPlan->name . ' Subscription',
                    'amount' => $amount,
                    'payer_name' => $user->username,
                    'payer_email' => $user->email,
                    'payer_phone' => $user->userDetail->mobile ?? '',
                    'webreturn_url' => route('payment.subscription.return'),
                    'callback_url' => route('payment.subscription.callback'),
                    'reference_1' => $subscription->id, // Store subscription ID for reference
                ]);

                $bizappayPage = $response->json();

                // Log the response for debugging
                Log::info('Bizappay bill creation response', ['response' => $bizappayPage]);

                // Check for error responses
                if (isset($bizappayPage['status']) && $bizappayPage['status'] === 'error') {
                    $errorMessage = $bizappayPage['msg'] ?? 'Unknown error';

                    Log::error('Bizappay bill creation error', [
                        'error_message' => $errorMessage,
                        'subscription_id' => $subscription->id
                    ]);

                    return redirect()->route('dashboard')
                        ->withError('Unable to process payment: ' . $errorMessage . '. Please try again later or contact customer support.');
                }

                if ($bizappayPage['status'] == "ok") {
                    // Save billcode to subscription
                    $subscription->update([
                        'bill_id' => $bizappayPage['billCode'],
                        'bill_type' => 'bizappay'
                    ]);

                    // Create Bizappay record
                    Bizappay::create([
                        'billcode' => $bizappayPage['billCode'],
                        'categorycode' => $categoryCode,
                        'paidamount' => $amount,
                        'paymentstatus' => 'Pending'
                    ]);

                    // Redirect to Bizappay payment page
                    return redirect($bizappayPage['url']);
                } else {
                    Log::error('Failed to create Bizappay bill - unexpected response', [
                        'response' => $bizappayPage,
                        'subscription_id' => $subscription->id
                    ]);

                    return redirect()->route('dashboard')
                        ->withError('We encountered an issue while setting up your payment. Please try again later or contact customer support.');
                }
            } catch (\Illuminate\Http\Client\RequestException $e) {
                Log::error('HTTP error during Bizappay bill creation', [
                    'error' => $e->getMessage(),
                    'status' => $e->getCode(),
                    'subscription_id' => $subscription->id
                ]);

                return redirect()->route('dashboard')
                    ->withError('Unable to connect to payment service. Please check your internet connection and try again later.');
            }
        } catch (\Exception $e) {
            Log::error('Unexpected exception in subscription payment process', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'subscription_id' => $subscription->id
            ]);

            return redirect()->route('dashboard')
                ->withError('An unexpected error occurred while processing your payment. Our team has been notified. Please try again later or contact customer support.');
        }
    }

    /**
     * Handle callback from payment gateway
     */
    public function handleCallback(Request $request)
    {
        Log::info('Payment callback received', $request->all());

        // Validate the request
        if (!$request->has('billcode')) {
            return response()->json(['status' => 'error', 'message' => 'Invalid callback data']);
        }

        // Find the subscription by bill_id
        $subscription = Subscription::where('bill_id', $request->billcode)->first();

        if (!$subscription) {
            Log::error('Subscription not found for billcode', ['billcode' => $request->billcode]);
            return response()->json(['status' => 'error', 'message' => 'Subscription not found']);
        }

        // Update Bizappay record
        $bizappay = Bizappay::where('billcode', $request->billcode)->first();

        if ($bizappay) {
            $bizappay->update([
                'invoice' => $request->billinvoice ?? null,
                'paymentstatus' => $request->billstatus ?? 'Unknown'
            ]);
        }

        // Update subscription status based on payment status
        if ($request->billstatus == '1') { // Successful payment
            $subscription->update([
                'status' => 'active'
            ]);

            Log::info('Payment successful, subscription activated', [
                'subscription_id' => $subscription->id,
                'billcode' => $request->billcode
            ]);
        } else {
            // Payment failed - convert to trial plan
            try {
                Log::info('Payment failed, converting to trial plan', [
                    'subscription_id' => $subscription->id,
                    'billcode' => $request->billcode
                ]);

                // First mark the current subscription as failed
                $subscription->update([
                    'status' => 'payment_failed'
                ]);

                // Then convert to trial plan
                $this->convertToTrialPlan($subscription);

                Log::info('Successfully converted failed payment to trial plan', [
                    'subscription_id' => $subscription->id
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to convert to trial plan in callback', [
                    'error' => $e->getMessage(),
                    'subscription_id' => $subscription->id
                ]);

                // If conversion fails, just mark as payment_failed
                if ($subscription->status !== 'payment_failed') {
                    $subscription->update([
                        'status' => 'payment_failed'
                    ]);
                }
            }
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Convert a failed subscription to a 12-day trial plan
     *
     * @param Subscription $subscription The subscription that failed payment
     * @return Subscription The new trial subscription
     */
    protected function convertToTrialPlan(Subscription $subscription)
    {
        Log::info('Converting failed subscription to 12-day trial', [
            'subscription_id' => $subscription->id,
            'company_id' => $subscription->company_id
        ]);

        try {
            // Find the "Starter - Trial" plan
            $trialPlan = SubscriptionPlan::where('name', 'Starter - Trial')
                ->where('is_active', true)
                ->first();

            if (!$trialPlan) {
                Log::error('Trial plan not found', [
                    'subscription_id' => $subscription->id
                ]);
                throw new \Exception('Trial plan not found');
            }

            DB::beginTransaction();

            // Cancel the failed subscription
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => Carbon::now(),
                'ends_at' => Carbon::now()
            ]);

            // Create a new subscription with the trial plan
            $trialSubscription = Subscription::create([
                'company_id' => $subscription->company_id,
                'subscription_plan_id' => $trialPlan->id,
                'starts_at' => Carbon::now(),
                'ends_at' => Carbon::now()->addDays(7),
                'trial_ends_at' => Carbon::now()->addDays(7),
                'status' => 'active'
            ]);

            DB::commit();

            Log::info('Successfully converted to trial plan', [
                'original_subscription_id' => $subscription->id,
                'trial_subscription_id' => $trialSubscription->id
            ]);

            return $trialSubscription;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to convert to trial plan', [
                'error' => $e->getMessage(),
                'subscription_id' => $subscription->id
            ]);
            throw $e;
        }
    }

    /**
     * Handle return from payment gateway
     */
    public function handleReturn(Request $request)
    {
        // This is where the user is redirected after payment
        // We'll check the payment status and show appropriate message

        if ($request->has('billcode')) {
            $subscription = Subscription::where('bill_id', $request->billcode)
                ->with('subscriptionPlan')
                ->first();

            if ($subscription) {
                if($request->get('status') == '1'){
                    return redirect()->route('payment.success')
                        ->with('success', 'Payment processed successfully.');
                } else {
                    try {
                        // Convert to trial plan
                        $this->convertToTrialPlan($subscription);

                        return redirect()->route('payment.fail')
                            ->with('warning', 'Your payment was not successful. We have automatically enrolled you in a 12-day trial plan so you can try our services. You can upgrade to a paid plan at any time.');
                    } catch (\Exception $e) {
                        Log::error('Error during payment failure handling', [
                            'error' => $e->getMessage(),
                            'subscription_id' => $subscription->id
                        ]);

                        return redirect()->route('payment.fail')
                            ->with('error', 'Payment failed. Please contact customer support for assistance.');
                    }
                }
            }
        }

        return redirect()->route('dashboard')
            ->withWarning('We could not verify your payment status. If you completed the payment, your subscription will be updated shortly.');
    }
}
