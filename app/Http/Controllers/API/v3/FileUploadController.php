<?php

namespace App\Http\Controllers\API\v3;

use App\Models\User;
use League\Csv\Reader;
use App\Models\Company;
use App\Models\UserDetail;
use Illuminate\Http\Request;
use App\Jobs\BulkRegisterUserJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use PhpOffice\PhpSpreadsheet\IOFactory;

class FileUploadController extends Controller
{
    /**
     * Allowed file types and their mime types
     */
    private const ALLOWED_TYPES = [
        'csv' => 'text/csv',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'xls' => 'application/vnd.ms-excel'
    ];

    /**
     * Process the uploaded file and save data to multiple tables
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processFileUpload(Request $request)
    {
        try {
            // Validate file and reCAPTCHA
            $rules = [
                'file' => 'required|mimes:csv,xlsx,xls',
                'g-recaptcha-response' => ['required', new \App\Rules\Recaptcha],
                'honeypot' => 'nullable|string|max:0' // Honeypot field - must be empty
            ];
            
            $val = $this->validate($request, $rules);

            $file = $request->file('file');
           // Parse the JSON string to array
            $columnMapping = json_decode($request->column_mapping, true);
            
            // Validate the column mapping structure
            $this->validateColumnMapping($columnMapping);

        BulkRegisterUserJob::dispatch($file,$columnMapping)->onQueue('bulk-register');
            
        return response()->json([
            'success' => true,
            'message' => 'File upload started successfully | Bulk registration',
        ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ],  400);
        }
    }

    // Add this method to validate the column mapping structure
    private function validateColumnMapping($mapping)
    {
        $requiredStructure = [
            'users' => ['name', 'email'],
            'user_details' => ['phone', 'address'],
            'companies' => ['name', 'address']
        ];

        foreach ($requiredStructure as $section => $fields) {
            if (!isset($mapping[$section])) {
                throw new \Exception("Missing mapping section: {$section}");
            }

            foreach ($fields as $field) {
                if (!isset($mapping[$section][$field])) {
                    throw new \Exception("Missing {$section} mapping for: {$field}");
                }
                
                if (empty($mapping[$section][$field])) {
                    throw new \Exception("Empty column mapping for {$section}.{$field}");
                }
            }
        }
    }
}
