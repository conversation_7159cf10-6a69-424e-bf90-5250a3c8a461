<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'bizappay' => [
        'key' => env('BIZAPPAY_KEY'),
        'url' => env('BIZAPPAY_URL'),
        'categoryCode' => env('BIZAPPAY_CATEGORY_CODE'),

        'keySandbox' => env('BIZAPPAY_KEY_SANDBOX'),
        'urlSandbox' => env('BIZAPPAY_URL_SANDBOX'),
        'categoryCodeSandbox' => env('BIZAPPAY_CATEGORY_CODE_SANDBOX'),
    ],

    'bayarcash' => [
        'api_token' => env('BC_API_TOKEN'),
        'api_secret_key' => env('BC_API_SECRET_KEY'),
        'sandbox' => env('BC_SANDBOX', true),
        'api_version' => env('BC_API_VERSION', 'v3'),
        'base_url' => env('BC_BASE_URL', 'https://api.console.bayar.cash/v3/'),
        'sandbox_base_url' => env('BC_SANDBOX_BASE_URL', 'https://api.console.bayarcash-sandbox.com/v3/'),
        'portal_key' => env('BC_PORTAL_KEY'),
    ],

    'recaptcha' => [
        'enabled' => env('RECAPTCHA_ENABLED', true),
        'site_key' => env('RECAPTCHA_V3_SITE_KEY'),
        'secret_key' => env('RECAPTCHA_V3_SECRET_KEY'),
        'threshold' => env('RECAPTCHA_THRESHOLD', 0.5),
    ]
];
